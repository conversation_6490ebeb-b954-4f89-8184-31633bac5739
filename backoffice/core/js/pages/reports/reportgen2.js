import defaultHeader from '../../components/default-header.js';


import timespentonsites from '../../components/reports/timespentonsites.js'
import timespentonservices from '../../components/reports/timespentonservices.js'
import mcneilreport from "../../components/reports/mcneilreport.js";
import materialusageonsites from '../../components/reports/materialusageonsites.js'
import photoreport from '../../components/reports/photoreport.js'
import basicservices from '../../components/reports/basicservices.js'
import photoreportuntagged from '../../components/reports/photoreportuntagged.js'
import photocount from '../../components/reports/photocount.js'
import employeetimesheet from '../../components/reports/employeetimesheet.js'
import formsnotcompletedinzones from '../../components/reports/formsnotcompletedinzones.js'
import formsreport from '../../components/reports/formsreport.js'
import formsReportExtended from '../../components/reports/forms-report-extended.js'
import workeractivity from '../../components/reports/workeractivity.js'
import siteactivity from '../../components/reports/siteactivity.js'
import snowtotalservice from '../../components/reports/snowtotalservice.js'
import basicservicesdetails from "../../components/reports/basicservicesdetails.js"
import snowStormsReport from '../../components/reports/snowstormsreport.js';
import snowStormReportAccuweather from '../../components/reports/snow-storm-report-accuweather.js';
import serviceMonitoringRequest from '../../components/reports/service-monitoring-request.js';
import fleetsummary from "../../components/reports/fleetsummary.js"
import accusalt from "../../components/reports/accusalt.js"
import servicedetailswitworkers from "../../components/reports/servicedetailswithworkers.js"
import serviceDetailsWorkersExtended from '../../components/reports/service-details-workers-extended.js'
import pricingReport from "../../components/reports/pricing-report.js"
import contractorSnowReport from "../../components/reports/contractor-snow-report.js"
import formsnotcompletedinzones2 from '../../components/reports/formsnotcompletedinzones2.js'
import formsNotCompletedInZonesExtended from '../../components/reports/forms-not-completed-in-zones-extended.js'
import earthdev from "../../components/reports/earthdev.js";
import sneller from "../../components/reports/sneller.js";
import snoweventreport1 from "../../components/reports/snoweventreport1.js";
import maptakeoffs from "../../components/reports/maptakeoffs.js";
import form2table from "../../components/reports/form2table.js";
import siteSnowForecast from '../../components/reports/site-snow-forecast.js'
import globalsnowreport from "../../components/reports/globalsnowreport.js";
import globalsnowreport2 from "../../components/reports/globalsnowreport2.js";
import globalsnowexceptionreport from "../../components/reports/globalsnowexceptionreport.js";
import clockedinreport from "../../components/reports/clockedinreport.js";
import siteSnowObservationsServices from '../../components/reports/site-snow-observations-services.js'
import checkinWithoutCheckout from '../../components/reports/checkin-without-checkout.js'
import perOccuranceJobCostReport from '../../components/reports/per-occurance-job-cost.js'
import siteSessionsServices from '../../components/reports/site-sessions-services.js'
import servicesTimes from '../../components/reports/services-times.js'
import timeSheetWeekly from '../../components/reports/time-sheet-weekly.js'
import serviceTrackerBySite from '../../components/reports/service-tracker-by-site.js'
import serviceTrackerBySiteClient from '../../components/reports/service-tracker-by-site-client.js'
import winterServiceTrackerWellsFargo from '../../components/reports/winter-service-tracker-wf.js'
import trueNorth1 from '../../components/reports/truenorth1.js'
import siteswithnoactivity from "../../components/reports/siteswithnoactivity.js";
import workorderServiceVerificationReport from "../../components/reports/workorder-service-verification-report.js";

import SumTestReport from "../../components/reports/sum-test-report.js";
import weeklyLandscapingReportWellsFargo from '../../components/reports/weekly-landscaping-report-wells-fargo.js';
import wellsFargoSnowReport from '../../components/reports/wells-fargo-snow-report.js';
import wellsFargoSnowReportExtended from '../../components/reports/wells-fargo-snow-report-extended.js';
import safebysix from "../../components/reports/safebysix.js";
import saltlogreport from "../../components/reports/saltlogreport.js";
import winterservices2table from "../../components/reports/winterservices2table.js";
import uspsSitesReport from '../../components/reports/usps-sites-report.js';
//Employee reports, for now to be added here (mike)
import employeeservicereport from "../../components/reports/employee-reports/employeeservicereport.js";
import employeepayrollsummaryreport from "../../components/reports/employee-reports/employeepayrollsummaryreport.js";
import weekpicker from "../../components/reports/weekpicker.js";

//NOTE: Should we register this for a whole app?


/*
TODO: create a sane system to tie these back without a lookup table
1 - Time Spent on Sites - timespentonsites.js - datatable - 50 lines
2 - Time Spent on Services (By Sites) - timespentonservices - datatable - 50 lines
3 - materialusageonsites - Material Usage (By Sites) - data-table - 40 lines
4 - photoreport - Photos Report - data-table 60 lines
5 - basicservices - Basic Services Completion Report - data-table 40 lines
6 - photoreportuntagged - Untagged Photos Report - data-table 40 lines
7 - photocount - Photo Count by Site - data-table 40 lines
9 - employeetimesheet - Employee Timesheet (ClockIn/Out) - data-table 40 lines
10 - formsnotcompletedinzones - data-table 25 lines
11 - formsreport - Forms Report - data-table 96 lines
12 - workeractivity - Worker Activity Report - data-table 40 lines
13 - siteactivity - Site Activity Report - data-table 40 lines
14 - basicservicesdetails - Basic Services with Details - data-table 40 lines
15 - servicedetailswitworkers - Service Details and Workers - data-table 50 lines
16 - earthdev
17 - maptakeoffs - Map Takeoffs Report - data-table 50 lines
19 - ??? - Form to Table
21 - clockedinreport - Currently clocked in workers report - data-table 40 lines
22 - pricingReportClient - data-table - 50 lines
23 - pricingReportContractor
27 - pricingReportContractorBatch
28 - pricingReportClientBatch
There are reports with code above 28 that are not documented here
42 - siteswithnoactivity
43 - workorderServiceVerificationReport
61 - Mcneil time tracker report only for user vendorId: 11284
65 - safebysix - only for vendor: 10573 TODO:Change name after checking with mike
66 - saltlogreport - only for vendor: 2092
70 - weeklyemployeeservicereport
71 - employeepayrollsummaryreport
101 - snowtotalservice
102 - fleetsummary
103 - accusalt
106 - formsnotcompletedinzones2
107 - globalsnowreport - handsontable - 40 lines
120 - uspssitesreport 
 */

export default {
    template: /*HTML*/`
    <div style="width:100%">
        <default-header title="Report Generator"></default-header>
        <v-layout style="padding: 0px 8px 8px 8px;">
            <v-flex>
                <v-card>
                    <v-card-title class="title">Report Filters</v-card-title>
                    <v-card-text class="pt-0 pb-10">
                        <v-layout wrap>
                            <v-flex xs12 sm12 md4>
                                <v-autocomplete outlined dense v-model="reportid" label="Select Report" hide-details :items="reports"
                                                :menu-props="{  }"></v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md6 v-if="reportid==17">
                                <v-autocomplete multiple outlined dense hide-details
                                                :class="{'pl-0': $vuetify.breakpoint.smAndDown, 'pl-2': $vuetify.breakpoint.mdAndUp}"
                                                v-model="reportfilter.maps" label="Select Maps" item-value="LayerID"
                                                item-text="LayerName" hide-details :items="maps" clearable>
                                    <template v-slot:selection="{ item, index }">
                                        <v-chip small v-if="index === 0"><span>{{ item.LayerName }}</span>
                                        </v-chip>
                                        <span class="grey--text caption" v-if="index === 1">(+
                                            {{ reportfilter.maps.length - 1 }}
                                            others)</span>
                                    </template>
                                </v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md3 v-if="reportid!=17 && reportid!=21 && reportid!=24 && reportid != 70 && reportid != 71 && reportid != 51">
                                <v-date-picker mode="dateTime" :model-config="modelConfig" :popover="{ placement: 'bottom-start', visibility: 'click' }" v-model="reportfilter.sDate" :max-date="new Date()">
                                    <template v-slot="{ inputValue, inputEvents, togglePopover }">
                                        <v-text-field
                                            @click="togglePopover()"
                                            outlined
                                            dense
                                            hide-details
                                            :class="{'pl-0': $vuetify.breakpoint.smAndDown, 'pl-2': $vuetify.breakpoint.mdAndUp}"
                                            :value="reportfilter.sDate"
                                            label="Start Date"
                                            readonly>
                                            <template v-slot:prepend-inner>
                                                <v-icon @click="togglePopover()">mdi-calendar</v-icon>
                                            </template>
                                        </v-text-field>
                                    </template>
                                </v-date-picker>
                            </v-flex>
                            <v-flex xs12 sm12 md3
                                    v-if="reportid!=17 && reportid!=21 && reportid!=24 && reportid!=26 && reportid!=39 && reportid != 70 && reportid != 71 && reportid != 51">
                                <v-date-picker mode="dateTime" :model-config="modelConfig" :popover="{ placement: 'bottom-start', visibility: 'click' }" v-model="reportfilter.eDate" :max-date="new Date()">
                                    <template v-slot="{ inputValue, inputEvents, togglePopover }">
                                        <v-text-field
                                            @click="togglePopover()"
                                            outlined
                                            dense
                                            hide-details
                                            :class="{'pl-0': $vuetify.breakpoint.smAndDown, 'pl-2': $vuetify.breakpoint.mdAndUp}"
                                            :value="reportfilter.eDate"
                                            label="End Date"
                                            readonly>
                                            <template v-slot:prepend-inner>
                                                <v-icon @click="togglePopover()">mdi-calendar</v-icon>
                                            </template>
                                        </v-text-field>
                                    </template>
                                </v-date-picker>
                            </v-flex>

                            <v-flex xs12 sm12 md3 v-if="reportid == 70 || reportid == 71">
                                <!--Week picker-->
                               <weekpicker 
                                 class="ml-2" style="align-items: center;display: flex;height: 38px;" v-model="weekSelectedUnix"></weekpicker>
                            </v-flex>
                            <v-flex xs12 sm12 md6 v-if="reportid == 51">
                                <v-autocomplete :class="{'pl-0': $vuetify.breakpoint.smAndDown, 'pl-2': $vuetify.breakpoint.mdAndUp}" outlined dense v-model="reportfilter.event" label="Select Event" hide-details item-value="sufe_id" item-text="sufe_event_name"
                                                :items="events" clearable></v-autocomplete>
                            </v-flex>
                            
                            <v-flex xs12 sm12 md2 style="text-align: end;">
                                <v-spacer></v-spacer>
                                <v-btn class="mx-2" tile depressed color="primary"
                                       style="background-color:white;text-transform:none; width: 100px;"
                                       @click.native="runReport">Go
                                </v-btn>
                            </v-flex>
                            <v-flex xs12 sm12 md4 v-if="reportid==10 || reportid==32">
                                <v-autocomplete outlined dense v-model="reportfilter.forms" label="Select Form" hide-details
                                                :items="forms" clearable></v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md4 v-if="reportid==19">
                                <v-autocomplete outlined dense v-model="reportfilter.forms" label="Select Form" hide-details
                                                :items="forms" clearable></v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md4 v-if="reportid==106">
                                <v-autocomplete outlined dense v-model="reportfilter.forms2" multiple label="Select Form" hide-details
                                                :items="forms" clearable>
                                    <template v-slot:selection="{ item, index }">
                                        <v-chip small v-if="index === 0"><span>{{ item.text }}</span>
                                        </v-chip>
                                        <span class="grey--text caption" v-if="index === 1">(+
                                            {{ reportfilter.forms2.length - 1 }}
                                            others)</span>
                                    </template>
                                </v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md3
                                    v-if="(reportid==10 || reportid==106 || reportid==32) && zones.length>0">
                                <v-autocomplete multiple
                                outlined dense hide-details
                                                :class="{'pl-0': $vuetify.breakpoint.smAndDown, 'pl-2': $vuetify.breakpoint.mdAndUp}"
                                                v-model="reportfilter.zone" label="Select Zone" item-value="mb_zone"
                                                item-text="mb_zone" hide-details :items="zones" clearable>


                                    <template v-slot:selection="{ item, index }">
                                        <v-chip small v-if="index === 0"><span>{{ item.mb_zone }}</span>
                                        </v-chip>
                                        <span class="grey--text caption" v-if="index === 1">(+
                                            {{ reportfilter.zone.length - 1 }}
                                            others)</span>
                                    </template>
                                </v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md3 v-if="reportid==10 || reportid==106 || reportid==32">
                                <v-autocomplete multiple
                                outlined dense hide-details
                                                :class="{'pl-0': $vuetify.breakpoint.smAndDown, 'pl-2': $vuetify.breakpoint.mdAndUp}"
                                                v-model="reportfilter.sites" label="Select Site" item-value="mb_id"
                                                item-text="mb_nickname" hide-details :items="buildinzones" clearable>
                                    <template v-slot:selection="{ item, index }">
                                        <v-chip small v-if="index === 0"><span>{{ item.mb_nickname }}</span>
                                        </v-chip>
                                        <span class="grey--text caption" v-if="index === 1">(+
                                            {{ reportfilter.sites.length - 1 }}
                                            others)</span>
                                    </template>
                                    <template v-slot:prepend-item>
                                        <v-list-item
                                          ripple
                                          @click="toggle"
                                        >

                                            <v-list-item-content>
                                                <v-list-item-title>Select All</v-list-item-title>
                                            </v-list-item-content>
                                        </v-list-item>
                                        <v-divider class="mt-2"></v-divider>
                                    </template>
                                </v-autocomplete>
                            </v-flex>
                            <!--Drop down showing contractors-->
                            <v-flex xs12 sm12 md4 v-if="reportid==27 || reportid==28" class="mt-4">
                                <v-autocomplete
                                outlined dense
                                  v-model="reportfilter.contractor"
                                  :label="reportid == 27 ? 'Select Contractor':'Select Client'"
                                  item-value="value"
                                  item-text="group"
                                  hide-details
                                  :items="contractors"
                                  clearable>
                                </v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md4
                                    v-if="reportid==13 || reportid==22 || reportid==23 || reportid==34 || reportid==35 || reportid==29 || reportid==30">
                                <v-autocomplete outlined dense v-model="reportfilter.sites" label="Select Site" item-value="mb_id"
                                                item-text="mb_nickname" hide-details :items="buildings" clearable>

                                </v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md4 v-if="reportid==12">
                                <v-autocomplete outlined dense v-model="reportfilter.workers" :loading="loading" label="Select Worker"
                                                item-value="email" item-text="contactname" hide-details :items="workers"
                                                clearable></v-autocomplete>
                            </v-flex>
                            <v-flex xs12 sm12 md4 v-if="reportid==39">
                                <v-autocomplete outlined dense v-model="reportfilter.workers" :loading="loading"
                                                label="Select Employee"
                                                hide-details :items="employees"
                                                clearable></v-autocomplete>
                            </v-flex>
                        </v-layout>
                    </v-card-text>
                </v-card>
            </v-flex>
        </v-layout>
        <timespentonsites v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==1"
                          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></timespentonsites>
        <timespentonservices v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==2"
                             :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></timespentonservices>

        <mcneilreport v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==61"
                      :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></mcneilreport>

        <materialusageonsites v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==3"
                              :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></materialusageonsites>
        <photoreport v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==4"
                     :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></photoreport>
        <basicservices v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==5"
                       :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></basicservices>
        <photoreportuntagged v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==6"
                             :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></photoreportuntagged>
        <photocount v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==7"
                    :sDate="reportfilter.sDate"
                    :eDate="reportfilter.eDate"></photocount>
                    <winterservices2table v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==57"
                    :sDate="reportfilter.sDate"
                    :eDate="reportfilter.eDate"></winterservices2table>
        <employeetimesheet v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==9"
                           :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></employeetimesheet>
        <form2table
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==19 && this.reportfilter.forms != null"
          :formid="this.reportfilter.forms"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></form2table>
        <formsnotcompletedinzones
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==10 && this.reportfilter.forms != null"
          :formid="this.reportfilter.forms" :zoneid="this.reportfilter.zone" :siteid="this.reportfilter.sites"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></formsnotcompletedinzones>
        <forms-not-completed-in-zones-extended
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==32 && this.reportfilter.forms != null"
          :formid="this.reportfilter.forms" :zoneid="this.reportfilter.zone" :siteid="this.reportfilter.sites"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></forms-not-completed-in-zones-extended>
        <formsreport v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==11"
                     :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></formsreport>
        <forms-report-extended v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==31"
                               :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></forms-report-extended>
        <workeractivity
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==12 && reportfilter.workers !=null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" :workerid="reportfilter.workers"></workeractivity>
        <earthdev v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==16"
                  :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></earthdev>
        <sneller v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==18"
                 :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></sneller>
        <snoweventreport1 v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==20"
                          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></snoweventreport1>
        <per-occurance-job-cost-report v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==36"
                                       :sDate="reportfilter.sDate"
                                       :eDate="reportfilter.eDate"></per-occurance-job-cost-report>
        <maptakeoffs v-if="search==17" :maps="reportfilter.maps"
        ></maptakeoffs>
        <clockedinreport v-if="search==21"
        ></clockedinreport>
        <time-sheet-weekly
          v-if="reportfilter.sDate != null && search==39 && reportfilter.sDate != null  && reportfilter.workers !=null"
          :sDate="reportfilter.sDate" :workerid="reportfilter.workers"></time-sheet-weekly>
          <sum-test-report
          v-if="reportfilter.sDate != null && search==62 && reportfilter.sDate != null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></sum-test-report>
        <siteactivity
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==13 && reportfilter.sites !=null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" :siteid="reportfilter.sites"></siteactivity>
        <basicservicesdetails v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==14"
                              :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></basicservicesdetails>
        <snowtotalservice v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==101"
                          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></snowtotalservice>
        <globalsnowreport v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==107"
                          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></globalsnowreport>
        <globalsnowreport2 v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==109"
                           :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></globalsnowreport2>
        <globalsnowexceptionreport v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==108"
                                   :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></globalsnowexceptionreport>
        <fleetsummary v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==102"
                      :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></fleetsummary>
        <accusalt v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==103"
                  :sDate="reportfilter.sDate"
                  :eDate="reportfilter.eDate"></accusalt>
        <servicedetailswitworkers v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==15"
                                  :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></servicedetailswitworkers>
        <service-details-workers-extended v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==33"
                                          :sDate="reportfilter.sDate"
                                          :eDate="reportfilter.eDate"></service-details-workers-extended>
        <site-snow-forecast v-if="search==24"></site-snow-forecast>
        <site-snow-observations-services v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==25"
                                         :sDate="reportfilter.sDate"
                                         :eDate="reportfilter.eDate"></site-snow-observations-services>
        <service-tracker-by-site v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==41"
                                 :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></service-tracker-by-site>
                                 <service-tracker-by-site-client v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==44"
                                 :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></service-tracker-by-site-client>
                                 <service-tracker-by-site v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==41"
                                 :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></service-tracker-by-site>
                                 <winter-service-tracker-wells-fargo v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==47"
                                 :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></winter-service-tracker-wells-fargo>
                                 <weekly-landscaping-report-wells-fargo v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==48"
                                 :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></weekly-landscaping-report-wells-fargo>
                                 <wells-fargo-snow-report v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==49"
                                 :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></wells-fargo-snow-report>
                                 <wells-fargo-snow-report-extended v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==50"
                                 :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></wells-fargo-snow-report-extended>
                                 <service-monitoring-request loader="report" v-if="reportfilter.event != null && search==51"
                                 :event="reportfilter.event"></service-monitoring-request>
        <checkin-without-checkout v-if="reportfilter.sDate != null && search==26"
                                  :sDate="reportfilter.sDate"></checkin-without-checkout>
        <site-sessions-services v-if="reportfilter.sDate != null && search==37 && reportfilter.eDate != null"
                                :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></site-sessions-services>
        <true-north1 v-if="reportfilter.sDate != null && search==40 && reportfilter.eDate != null"
                     :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></true-north1>
        <services-times v-if="reportfilter.sDate != null && search==38 && reportfilter.eDate != null"
                        :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></services-times>
                        <snow-storms-report
                        v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==45"
                        :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" ></snow-storms-report>
                        <snow-storm-report-accuweather
                        v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==46"
                        :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" ></snow-storm-report-accuweather>
        <pricingReport
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==22 && reportfilter.sites!=null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" :sites="reportfilter.sites" weatherProvider="NOAA"
          :contractFor="'CLIENT'"></pricingReport>

        <pricingReport
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==23 && reportfilter.sites!=null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" :sites="reportfilter.sites" weatherProvider="NOAA"
          :contractFor="'CONTRACTOR'"></pricingReport>
        <pricingReport
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==29 && reportfilter.sites!=null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" :sites="reportfilter.sites" weatherProvider="TW"
          :contractFor="'CONTRACTOR'"></pricingReport>
        <pricingReport
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==30 && reportfilter.sites!=null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" :sites="reportfilter.sites" weatherProvider="TW"
          :contractFor="'CLIENT'"></pricingReport>

    <!--    <pricingReport
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==34 && reportfilter.sites!=null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" :sites="reportfilter.sites" weatherProvider="WW"
          :contractFor="'CONTRACTOR'"></pricingReport>

        <pricingReport
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==35 && reportfilter.sites!=null"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate" :sites="reportfilter.sites" weatherProvider="WW"
          :contractFor="'CLIENT'"></pricingReport> -->

        <contractorSnowReport
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==27" :sDate="reportfilter.sDate"
          :eDate="reportfilter.eDate" :contractorId="reportfilter.contractor" :contractFor="'CONTRACTOR'"
        ></contractorSnowReport>
        <contractorSnowReport
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==28" :sDate="reportfilter.sDate"
          :eDate="reportfilter.eDate" :contractorId="reportfilter.contractor"
          :contractFor="'CLIENT'"></contractorSnowReport>
        <formsnotcompletedinzones2
          v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==106 && this.reportfilter.forms2.length>0"
          :formid="this.reportfilter.forms2" :zoneid="this.reportfilter.zone" :siteid="this.reportfilter.sites"
          :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></formsnotcompletedinzones2>

    <siteswithnoactivity
      v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==42"
      :sDate="reportfilter.sDate" 
      :eDate="reportfilter.eDate"
    ></siteswithnoactivity>

      <workorder-service-verification-report
        v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==43"
        :sDate="reportfilter.sDate"
        :eDate="reportfilter.eDate">
      </workorder-service-verification-report>
        
    <employeeservicereport
      v-if="weekSelectedUnix != null && search==70"
      :week-selected="weekSelectedUnix"
    />

        <employeepayrollsummaryreport
          v-if="weekSelectedUnix != null && search==71"
          :week-selected="weekSelectedUnix"
        />

        <safebysix v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==65" :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"/>
        
        <saltlogreport v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==66" :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"/>
        <usps-sites-report v-if="reportfilter.sDate != null && reportfilter.eDate != null && search==120" :sDate="reportfilter.sDate" :eDate="reportfilter.eDate"></usps-sites-report>
      <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
                :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">
        {{ snackbar.text }}
        <v-btn color="pink" text @click="snackbar.snackbar = false">
            Close
        </v-btn>
    </v-snackbar>
</div>`,
    components: {
    contractorSnowReport, timespentonsites, winterservices2table, checkinWithoutCheckout, pricingReport, siteSnowObservationsServices, siteSnowForecast, serviceDetailsWorkersExtended, clockedinreport, formsNotCompletedInZonesExtended, formsReportExtended, form2table, globalsnowreport, globalsnowreport2, globalsnowexceptionreport, formsnotcompletedinzones2, maptakeoffs, sneller, snoweventreport1, earthdev, accusalt, servicedetailswitworkers, basicservicesdetails, fleetsummary, formsreport, snowtotalservice, workeractivity, siteactivity, formsnotcompletedinzones, employeetimesheet, timespentonservices, materialusageonsites, photoreport, basicservices, photoreportuntagged, photocount, perOccuranceJobCostReport, siteSessionsServices, servicesTimes, timeSheetWeekly, serviceTrackerBySite, mcneilreport, trueNorth1, siteswithnoactivity, workorderServiceVerificationReport, defaultHeader, serviceTrackerBySiteClient, snowStormsReport, snowStormReportAccuweather, SumTestReport, winterServiceTrackerWellsFargo, weeklyLandscapingReportWellsFargo, wellsFargoSnowReport, wellsFargoSnowReportExtended, serviceMonitoringRequest, employeeservicereport, employeepayrollsummaryreport, weekpicker, safebysix, saltlogreport, uspsSitesReport
    },
    
    mounted() {

        if (this.$store.getters.getAccessCode == 'aab085461de182608ee9f607f3f7d18f') {
            this.reports.push({
                value: 61,
                text: "McNeill Time Tracker"
            });
        }
        if (this.$store.getters.getAccessCode == '840d68cbbbfa627cd4635408a6c82009') {
            this.reports.push({
                value: 62,
                text: "Test report"
            });
        }
        if (this.$store.getters.getAccessCode == '17446a8ae7dbf7e2c2535ba49340b4b9') {
            this.reports.push({
                value: 65,
                text: "Safe by Six"
            });
        }
        if (this.$store.getters.getAccessCode == '801272ee79cfde7fa5960571fee36b9b') {
            this.reports.push({
                value: 66,
                text: "Salt Log"
            });
        }
        if (this.$store.getters.getAccessCode == 'a933075894d2fccffdaa2a492a4a12da') {
            this.reports.push({
                value: 40,
                text: "True North Report - 1"
            });
        }
        if (this.$store.getters.getAccessCode == '148148d62be67e0916a833931bd32b26') {
            this.reports.push({
                value: 31,
                text: "Forms Report - Extended"
            })
            this.reports.push({
                value: 32,
                text: "Forms not submitted - Extended"
            })
            this.reports.push({
                value: 33,
                text: "Service Details Workers - Extended"
            })
        }
        if (this.$store.getters.getAccessCode == '716e1b8c6cd17b771da77391355749f3' || this.$store.getters.getAccessCode == '8f85517967795eeef66c225f7883bdcb') {
            this.reports.push({
                value: 18,
                text: "Sneller Custom Activity Report"
            })
        }
        if (this.$store.getters.getAccessCode == 'aab085461de182608ee9f607f3f7d18f') {
            this.reports.push({
                value: 39,
                text: "Employee Timesheet Weekly"
            })
        }
        if (this.$store.getters.getAccessCode == '219ece62fae865562d4510ea501cf349') {
            this.reports.push({
                value: 36,
                text: "Per Occurrence Job Cost Report"
            })
        }
        if (this.$store.getters.getAccessCode == '2cbd9c540641923027adb8ab89decc05' || this.$store.getters.getAccessCode == '8f85517967795eeef66c225f7883bdcb' || this.$store.getters.getAccessCode == '5fc34ed307aac159a30d81181c99847e' || this.$store.getters.getAccessCode == 'f40ee694989b3e2161be989e7b9907fc' || this.$store.getters.getAccessCode == '3ce3bd7d63a2c9c81983cc8e9bd02ae5' || this.$store.getters.getAccessCode == '81c8727c62e800be708dbf37c4695dff') {
            this.reports.push({
                value: 20,
                text: "Snow Event Report - I"
            })
        }
        if (this.$store.getters.getAccessCode == 'b207f5c56605a9d1a22e1e134fe95ba9' || this.$store.getters.getAccessCode == '8f85517967795eeef66c225f7883bdcb') {
            this.reports.push({
                value: 16,
                text: "EarthDev Custom Report"
            })
        }
        if (this.$store.getters.getAccessCode == '955cb567b6e38f4c6b3f28cc857fc38c') {
            this.reports.push({
                value: 101,
                text: "Basic Services with Snow Totals"
            })
            this.reports.push({
                value: 107,
                text: "Global Snow Report",
            })
            this.reports.push({
                value: 108,
                text: "Global No Service Snow Report",
            })
            this.reports.push({
                value: 109,
                text: "Global Snow Report - 2",
            })
        }
        if (this.$store.getters.getAccessCode == '8ccfb1140664a5fa63177fb6e07352f0')
            this.reports.push({
                value: 38,
                text: "Service Times"
            })
        if (this.$store.getters.getAccessCode == '1013c8b99e603831ad123eab4b27660f' || this.$store.getters.getAccessCode == 'ea81a3d20bf98ef2c9bef9dc24ec777a') {
            this.reports.push({
                value: 106,
                text: "Forms not submitted report"
            })
        }
        else {
            this.reports.push({
                value: 10,
                text: "Forms not submitted report"
            })
        }

        let l = this.myservices.find(function (element) {
            return element.vs_provider == "ACCUSALT" ;
        });
        if (typeof l != 'undefined' && !this.isClientViewEnabled) {
            this.reports.push({
                value: 103,
                text: "Accusalt Report"
            })

        }

        if (hybridUser) {
            this.reports.push({
                value: 41,
                text: "Service Tracker By Site"
            })
            this.reports = this.reports.filter(report => [24, 41].includes(report.value))
        }
        if (this.isClientViewEnabled) {
            this.reports.push({
                value: 44,
                text: "Service Tracker By Site"
            })
            this.reports.push({
                value: 47,
                text: "Winter Service Tracker"
            })
            this.reports.push({
                value: 48,
                text: "Weekly Landscape Report"
            })
            this.reports.push({
                value: 49,
                text: "Snow Season Summary Report"
            })
            this.reports.push({
                value: 50,
                text: "Snow Season Summary Report - Photos"
            })
            this.reports.push({
                value: 51,
                text: "Service Request CPS"
            })
            this.reports.push({
                value: 57,
                text: "Winter Services Forms To Table"
            })

            if (this.isWellsFargo)
                this.reports = this.reports.filter(report => [11, 47, 48, 19, 49, 57].includes(report.value))
            else if (this.isCPS)
                this.reports = this.reports.filter(report => [11, 51].includes(report.value))
            else if (this.isCVS)
                this.reports = this.reports.filter(report => [11].includes(report.value))
            else if (this.isCasey)
                this.reports = this.reports.filter(report => [11].includes(report.value))
            else if (this.isUSPS) {
                this.reports = this.reports.filter(report => [11,19].includes(report.value))
                this.reports.push({
                    value: 120,
                    text: "Sites Report"
                });
            } else
                this.reports = this.reports.filter(report => [44].includes(report.value))
        }
        this.reports = this.reports.sort(function (a, b) {
            return a.text.localeCompare(b.text)
        });

        this.updateData();
    },
    async created() {
        let rId = this.$route.query.report;
        let start = this.$route.query.start;
        let end = this.$route.query.end;
        if (rId) {
            this.reportid = parseInt(rId);
        }
        if (start) {
            this.reportfilter.sDate = start;
        }
        if (end) {
            this.reportfilter.eDate = end;
        }
        let req = await fetch(`${myBaseURL}/node/vpics/integrations`)
        let data = await req.json()
        if (data.gtStatus || data.fcStatus || data.cpStatus || data.fsStatus || data.vnStatus || data.azStatus || data.usStatus || data.ntStatus || data.ezStatus || data.ssStatus || data.linxupStatus || data.intouchStatus || data.rdStatus || data.vsStatus || data.rhStatus || data.osStatus || data.fmStatus) {
            this.reports.push({
                value: 102,
                text: "Fleet Summary Report"
            })
        }
        if (data.twStatus) {
            this.reports.push({
                value: 29,
                text: "Pricing Report Contractor - TrueWeather"
            })
            this.reports.push({
                value: 30,
                text: "Pricing Report Client - TrueWeather"
            })
        }
        /*  if(data.wwStatus)
          {
              this.reports.push({
                  value: 34,
                  text: "Pricing Report Contractor - WeatherWorks"
              })
              this.reports.push({
                  value: 35,
                  text: "Pricing Report Client - WeatherWorks"
              })
          } */
        if ((data.wwStatus || data.twStatus) && !this.isClientViewEnabled) {
            this.reports.push({
                value: 45,
                text: "Snow Storms Report"
            })
        }
    },
    data: function () {
        return {
            modelConfig: {
                type: 'string',
                mask: 'MM/DD/YY hh:mm a', // Match the previous format
            },
            integrations: {},
            loading: false,
            stormsloading: false,
            search: null,
            reportid: null,
            range: {},
            trigger: false,
            weekSelectedUnix: null,
            reportfilter: {
                rType: null,
                sDate: null,
                eDate: null,
                sites: null,
                zones: null,
                services: 'All',
                materials: [],
                forms: null,
                forms2: [],
                workers: null,
                activities: [],
                stormid: null,
                event: null,
                maps: []
            },
            reports: [{
                value: 1,
                text: "Time Spent on Sites"
            },
            {
                value: 2,
                text: "Time Spent on Services (By Sites)"
            },
            {
                value: 3,
                text: "Material Usage (By Sites)"
            },
            {
                value: 4,
                text: "Photos Report"
            },
            {
                value: 5,
                text: "Basic Services Completion Report"
            },
            {
                value: 6,
                text: "Untagged Photos Report"
            },
            {
                value: 7,
                text: "Photo Count by Site"
            },
            {
                value: 9,
                text: "Employee Timesheet (ClockIn/Out)"
            },
            {
                value: 11,
                text: "Forms Report"
            },
            {
                value: 12,
                text: "Worker Activity Report"
            },
            {
                value: 13,
                text: "Site Activity Report"
            },
            {
                value: 14,
                text: "Basic Services with Details"
            },
            {
                value: 15,
                text: "Service Details and Workers"
            },
            {
                value: 17,
                text: "Map Takeoffs Report"
            },
            {
                value: 19,
                text: "Form to Table"
            }, {
                value: 21,
                text: "Currently clocked in workers report"
            }, {
                value: 22,
                text: "Pricing Report Client"
            }, {
                value: 23,
                text: "Pricing Report Contractor"
            }, {
                value: 24,
                text: "Snow Forecast"
            }, {
                value: 25,
                text: "Snow Observations Service Status"
            }, {
                value: 26,
                text: "CheckIn without CheckOut"
            }, {
                value: 27,
                text: "Pricing Report Contractor Batch"
            }, {
                value: 28,
                text: "Pricing Report Client Batch"
            }, {
                value: 37,
                text: "Site Session Services Report"
            }, {
                value: 42,
                text: "Sites with no Activity"
            }, {
                value: 43,
                text: "Work Order Service Verification Report"
            },
            {
                value: 46,
                text: "Snow Events Report - Accuweather"
            },
                {
                    value: 70,
                    text: "Employee Service Report"
                },
                {
                    value: 71,
                    text: "Employee Payroll Summary Report"
                }
            ],
            maps: [],
            dprops: {

                clearable: true
            },
            snackbar: {
                snackbar: false,
                y: 'bottom',
                x: 'right',
                mode: '',
                timeout: 6000,
                text: ''
            },
            dpropsmobile: {
                clearable: true,
                hideDetails: true,
            },
            mprops: {
                fullWidth: true
            },

        }
    },
    watch: {
        myservices(val) {
            let l = val.find(function (element) {
                return element.vs_provider == "ACCUSALT";
            });
            let k = this.reports.find(function (element) {
                return element.value == 103;
            });
            if (typeof l != 'undefined' && typeof k == 'undefined' && !this.isClientViewEnabled) {
                this.reports.push({
                    value: 103,
                    text: "Accusalt Report"
                })

            }
            this.reports = this.reports.sort(function (a, b) {
                return a.text.localeCompare(b.text)
            });
        },
        'reportfilter.zone'(newVal) {
            Vue.set(this.reportfilter, "sites", null);
            this.search = null;
        },
        'reportfilter.sites'(newVal) {
            this.search = null;
        },
        'reportfilter.workers'(newVal) {
            this.search = null;
        },
        'reportfilter.forms'(newVal) {
            this.search = null;
        },
        'reportfilter.forms2'(newVal) {
            this.search = null;
        },
        'reportfilter.stormid'(newVal) {
            this.search = null;
        },
        'reportfilter.maps'(newVal) {
            this.search = null;
        },
    },

    methods: {
        toggle() {
            this.$nextTick(() => {
                if (this.reportfilter.sites == null) {
                    this.reportfilter.sites = this.buildinzones.map(a => a.mb_id)
                    return;
                }
                if (this.reportfilter.sites.length === this.buildinzones.length) {
                    this.reportfilter.sites = null
                } else {
                    this.reportfilter.sites = this.buildinzones.map(a => a.mb_id)
                }
            })
        },
        async updateData() {
            const formData = new FormData();
            formData.append('accessCode', store.get('accessCode'));
            let layers = await fetch(myBaseURL + '/node/maps/map-layers', { method: 'POST', body: formData })
            let layersJson = await layers.json()
            Vue.set(this, 'maps', layersJson.filter(file => file.AutoSave == '0'))
        },
        runReport() {
            if (this.reportid == null) {
                this.snackbar.text = "Please select a report to run";
                this.snackbar.snackbar = true;
            }
            else if ((this.reportfilter.sDate == null || this.reportfilter.eDate == null) && this.reportid != 17 && this.reportid != 21 && this.reportid != 24 && this.reportid != 26 && this.reportid != 39 && this.reportid != 70 && this.reportid != 71 && this.reportid != 51) {
                this.snackbar.text = "Please select start and end date";
                this.snackbar.snackbar = true;
            }
            else if ((this.reportid == 13 || this.reportid == 22) && this.reportfilter.sites == null) {
                this.snackbar.text = "Please select a site to run this report";
                this.snackbar.snackbar = true;
            }
            else if (this.reportid == 10 && this.reportfilter.forms == null) {
                this.snackbar.text = "Please select a form to run this report";
                this.snackbar.snackbar = true;
            }
            else if ((this.reportid == 12 || this.reportid == 39) && this.reportfilter.workers == null) {
                this.snackbar.text = "Please select a worker to run this report";
                this.snackbar.snackbar = true;
            }
            else if (this.reportid == 17 && this.reportfilter.maps.length == 0) {
                this.snackbar.text = "Please select maps to run this report";
                this.snackbar.snackbar = true;
            }
            else if (this.reportid == 51 && this.reportfilter.event == null) {
                this.snackbar.text = "Please select an event to run this report";
                this.snackbar.snackbar = true;
            }
            let k = this.reportid
            this.search = null
            Vue.nextTick(function () { this.search = k; }.bind(this))
        },
        mySorter(a, b) {
            let x = a.text.toLowerCase();
            let y = b.text.toLowerCase();
            if (a.value == null)
                return -1;
            return ((x < y) ? -1 : ((x > y) ? 1 : 0));
        },
    },
    computed: {
        isClientViewEnabled() {
            if (typeof window.clientViewEnabled == 'undefined')
                return false;
            else
                return window.clientViewEnabled;
        },
        isWellsFargo() {
            if (this.isClientViewEnabled == true && this.$store.getters.getAccessCode == 'cd14d7eac0e4638dca6ef495702dd013')
                return true;
            else
                return false;
        },
        isCPS() {
            if (this.isClientViewEnabled == true && this.$store.getters.getAccessCode == '00d8e45251394d51c6def3c960da9a6e')
                return true;
            else
                return false;
        },
        isCasey() {
            if (this.isClientViewEnabled == true && this.$store.getters.getAccessCode == '6a1cf9ff786388fe8796c57ec63d1075')
                return true;
            else
                return false;
        },
        isUSPS() {
            if (this.isClientViewEnabled == true && this.$store.getters.getAccessCode == '1ed3f6c5e3c6aad50b5e8a32f3a05d61')
                return true;
            else
                return false;
        },
        isCVS() {
            if (this.isClientViewEnabled == true && this.$store.getters.getAccessCode == '3d76cc27d29c076b2c374df9ac79d6a8')
                return true;
            else
                return false;
        },
        myservices: {
            get() {

                return this.$store.getters.getServices;
            }

        },
        currentTime: {
            get() {
                return dateFns.format(new Date(), 'MM/DD/YY hh:mm a');
            }
        },
        buildings: {
            get() {
                return _.filter(this.$store.getters.getBuildings, {
                    'mb_status': "1"
                });
            },
        },
        employees: {
            get() {
                return this.$store.getters.getEmployees;
            }
        },
        contractors: {
            get() {
                if (this.reportid == 27) {
                    return this.$store.getters.getContractorsForReports
                } else if (this.reportid == 28) {
                    return this.$store.getters.getCustomers.filter(gc => gc.sf_contact_company_name != '')
                }
            }
        },
        buildinzones: {
            get() {
                if (this.zones.length == 0 || this.reportfilter.zone == null)
                    return this.buildings;
                else {
                    if (this.reportfilter.zone == null)
                        return this.buildings;
                    else {
                        if (!Array.isArray(this.reportfilter.zone)) {
                            let l = this.buildings.filter(function (element) {
                                return element.mb_zone == this.reportfilter.zone
                            }.bind(this));
                            return l;
                        }
                        else {
                            let l = this.buildings.filter(function (element) {
                                return this.reportfilter.zone.includes(element.mb_zone)
                            }.bind(this));
                            return l;
                        }

                    }
                }
            }
        },
        zones: {
            get() {
                let l = _.uniqBy(this.buildings, 'mb_zone');
                l = l.filter(function (element) {
                    return element.mb_zone != null && element.mb_zone != "";
                });

                return l;
            }
        }, 
        mini: {
            get() {
                return this.$store.getters.getMini;
            },
            set(value) {
                this.$store.commit('setMini', value)
            }
        }
    },
    asyncComputed: {
        events: {
            async get() {
                const response = await fetch('/node/events/');
                const data = await response.json();
                return data;
            },
            default() {
                return [];
            }
        },
        forms: {
            async get() {
              let files = [];
              const params = new URLSearchParams();
              params.append('accessCode', myvid);

              try {
                if(this.isClientViewEnabled)
                {
                    const response = await fetch('/node/forms/clientview-forms');
                    const data = await response.json();
                    for (let i = 0; i < data.length; i++) {
                        //push if new name, otherwise add to existing
                        let existing = files.find(f => f.text == data[i].name);
                        if (existing) {
                            //only if not already in the array
                            if (!existing.value.includes(data[i].id))
                                existing.value.push(data[i].id);
                        }
                        else {
                        files.push({
                            value: [data[i].id],
                            text: data[i].name,
                            sites: []
                        });
                    }
                    }


                    files.sort(this.mySorter);
                    return files;
                }
                const response = await fetch(this.$store.state.getFormsURL);
                const data = await response.json();

                for (let i = 0; i < data.length; i++) {
                  files.push({
                    value: data[i].sf_id,
                    text: data[i].sf_form_name,
                    sites: data[i].sf_form_site
                  });
                }

                files.sort(this.mySorter);
                return files;
              } catch (e) {
                console.log(e);
                return [];
              }
            },
            default() {
                return [];
            }
        },
        workers: {
            get() {
                if (this.reportfilter.sDate == null || this.reportfilter.eDate == null || this.reportid != 12) {
                    return [];
                }
                this.loading = true;
                let self = this;
                let start = dateFns.parse(this.reportfilter.sDate).getTime() / 1000
                let end = dateFns.parse(this.reportfilter.eDate).getTime() / 1000
                return axios.get(myBaseURL + '/vpics/bogetworkersinrange', {
                    params: {
                        accessCode: this.$store.getters.getAccessCode,
                        startdate: start,
                        enddate: end
                    }
                }).then(function (response) {
                    let results = []
                    results = response.data.map(function (v) {

                        let local = self.employees.find(e => e.email == v.email)

                        if (local != undefined) {
                            if (local.fname != null)
                                v.contactname = local.fname + " " + local.lname + " "
                            else
                                v.contactname = local.email;
                        } else {
                            if (v.fname != null)
                                v.contactname = v.fname + " " + v.lname + " "
                            else
                                v.contactname = v.email;
                        }
                        return v;
                    }).sort(function (a, b) {
                        return a.contactname.localeCompare(b.contactname)
                    });

                    self.loading = false;
                    return _.uniqBy(results, 'email');

                })
            },
            default() {
                return [];
            }
        },
    }
}
